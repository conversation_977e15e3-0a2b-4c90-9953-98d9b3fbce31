// SummaryCard.tsx
// Dashboard özet kartları için component

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../common/Card';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { cn } from '../../lib/utils';

export interface SummaryCardProps {
  title: string;
  value: number | string;
  change: string;
  icon?: React.ReactNode;
  isLoading?: boolean;
  className?: string;
}

export const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  change,
  icon,
  isLoading = false,
  className
}) => {
  const isPositiveChange = change.startsWith('+');
  const isNegativeChange = change.startsWith('-');

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-4 xl:p-6">
        <CardTitle className="text-xs xl:text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        {icon && (
          <div className="h-3 w-3 xl:h-4 xl:w-4 text-gray-400">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent className="p-4 xl:p-6 pt-0">
        {isLoading ? (
          <div className="flex items-center justify-center h-12 xl:h-16">
            <LoadingSpinner size="sm" />
          </div>
        ) : (
          <div className="space-y-1">
            <div className="text-lg xl:text-2xl font-bold text-gray-900">
              {typeof value === 'number' && title.toLowerCase().includes('kazanç')
                ? `₺${value.toLocaleString('tr-TR', { minimumFractionDigits: 2 })}`
                : value
              }
            </div>
            <p className={cn(
              'text-xs flex items-center',
              isPositiveChange && 'text-green-600',
              isNegativeChange && 'text-red-600',
              !isPositiveChange && !isNegativeChange && 'text-gray-500'
            )}>
              <span className="mr-1">
                {isPositiveChange && '↗'}
                {isNegativeChange && '↘'}
                {!isPositiveChange && !isNegativeChange && '→'}
              </span>
              <span className="hidden sm:inline">{change} önceki güne göre</span>
              <span className="sm:hidden">{change}</span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
