// dashboard.routes.ts
// Dashboard API route tanımları

import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { DashboardController } from '../controllers/dashboard.controller.js';
import { authenticateToken, requireRole } from '../middlewares/auth.middleware.js';
import { validate } from '../middlewares/validation.middleware.js';
import { ordersQuerySchema } from '../validators/dashboard.validator.js';

export const createDashboardRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const dashboardController = new DashboardController(prisma);

  // Tüm dashboard endpoint'leri authentication gerektirir
  router.use(authenticateToken);

  // Dashboard verilerine erişim için minimum CASHIER yetkisi gerekli
  const dashboardRoles = ['SUPER_ADMIN', 'ADMIN', 'BRANCH_MANAGER', 'CASHIER', 'WAITER'];

  // GET /api/dashboard/health - Sağ<PERSON><PERSON>k kontrolü (authentication gerektirmez)
  router.get('/health', dashboardController.getHealth);

  // GET /api/dashboard/summary - Genel özet verileri
  router.get(
    '/summary',
    requireRole(dashboardRoles),
    dashboardController.getSummary
  );

  // GET /api/dashboard/popular-dishes - Popüler yemekler
  router.get(
    '/popular-dishes',
    requireRole(dashboardRoles),
    dashboardController.getPopularDishes
  );

  // GET /api/dashboard/out-of-stock - Stokta olmayan ürünler
  router.get(
    '/out-of-stock',
    requireRole(dashboardRoles),
    dashboardController.getOutOfStock
  );

  // GET /api/dashboard/orders - Sipariş listeleri (durum filtresi ile)
  router.get(
    '/orders',
    requireRole(dashboardRoles),
    validate(ordersQuerySchema),
    dashboardController.getOrders
  );

  return router;
};
